'use client';

import { useEffect, useCallback, useRef } from 'react';
import { useAudioStore, AudioSource, AudioSourceType } from './audio-store';

interface UseGlobalMusicControlOptions {
  sourceId: string;
  sourceType: AudioSourceType;
  sourceName: string;
  onPlayStart?: () => void;
  onPause?: () => void;
  onStop?: () => void;
  persistOnUnmount?: boolean; // New flag to prevent cleanup on unmount
}

interface GlobalMusicControlReturn {
  // State
  isActive: boolean;
  hasControl: boolean;
  canPlay: boolean;
  isRegistered: boolean;

  // Actions
  requestControl: () => boolean;
  releaseControl: () => void;
  notifyPlayStart: (track?: AudioSource['currentTrack']) => void;
  notifyPause: () => void;
  notifyStop: () => void;
  updateState: (updates: Partial<AudioSource>) => void;

  // Utilities
  pauseOthers: () => void;
  pauseAll: () => void;
}

/**
 * Custom hook for integrating components with the global music control system.
 * Provides a simplified interface for registering audio sources and coordinating playback.
 */
export function useGlobalMusicControl(options: UseGlobalMusicControlOptions): GlobalMusicControlReturn {
  const {
    sourceId,
    sourceType,
    sourceName,
    onPlayStart,
    onPause,
    onStop,
    persistOnUnmount = false,
  } = options;

  // Audio store actions
  const registerAudioSource = useAudioStore((state) => state.registerAudioSource);
  const unregisterAudioSource = useAudioStore((state) => state.unregisterAudioSource);
  const updateAudioSource = useAudioStore((state) => state.updateAudioSource);
  const requestAudioControl = useAudioStore((state) => state.requestAudioControl);
  const releaseAudioControl = useAudioStore((state) => state.releaseAudioControl);
  const pauseAllExcept = useAudioStore((state) => state.pauseAllExcept);
  const pauseAllSources = useAudioStore((state) => state.pauseAllSources);
  const globalAudioControl = useAudioStore((state) => state.globalAudioControl);

  // Track if this source is registered
  const isRegisteredRef = useRef(false);
  const hasControlRef = useRef(false);

  // Check if this source is currently active
  const isActive = globalAudioControl.currentActiveSource === sourceId;
  const hasControl = hasControlRef.current;
  const canPlay = globalAudioControl.mutualExclusionEnabled ? isActive : true;
  const isRegistered = isRegisteredRef.current;

  // Create stable function references to avoid infinite loops
  const pauseFunctionRef = useRef<() => void>(() => {});
  const playFunctionRef = useRef<() => void>(() => {});

  // Update function references when callbacks change
  pauseFunctionRef.current = () => {
    if (onPause) {
      onPause();
    }
  };

  playFunctionRef.current = () => {
    if (onPlayStart) {
      onPlayStart();
    }
  };

  // Create stable function wrappers
  const pauseFunction = useCallback(() => {
    pauseFunctionRef.current?.();
  }, []);

  const playFunction = useCallback(() => {
    playFunctionRef.current?.();
  }, []);

  // Register audio source on mount
  useEffect(() => {
    if (!isRegisteredRef.current) {
      const audioSource: AudioSource = {
        id: sourceId,
        type: sourceType,
        name: sourceName,
        isPlaying: false,
        volume: 70,
        isMuted: false,
        currentTrack: null,
        currentTime: 0,
        duration: 0,
        pauseFunction,
        playFunction,
      };

      registerAudioSource(audioSource);
      isRegisteredRef.current = true;

      console.log(`Global Music Control: Registered ${sourceId} (${sourceType})`);
    }

    // Cleanup on unmount (only if not persisting)
    return () => {
      if (isRegisteredRef.current && !persistOnUnmount) {
        unregisterAudioSource(sourceId);
        isRegisteredRef.current = false;
        hasControlRef.current = false;
        console.log(`Global Music Control: Unregistered ${sourceId}`);
      } else if (persistOnUnmount) {
        console.log(`Global Music Control: Persisting ${sourceId} on unmount`);
      }
    };
  }, [sourceId, sourceType, sourceName, pauseFunction, playFunction, registerAudioSource, unregisterAudioSource, persistOnUnmount]);

  // Update pause/play functions when they change (only when the source is registered)
  useEffect(() => {
    if (isRegisteredRef.current) {
      updateAudioSource(sourceId, {
        pauseFunction,
        playFunction,
      });
    }
  }, [sourceId, pauseFunction, playFunction, updateAudioSource]);

  // Request control function
  const requestControl = useCallback(() => {
    console.log(`Global Music Control ${sourceId}: Requesting audio control`);
    const granted = requestAudioControl(sourceId);
    hasControlRef.current = granted;
    console.log(`Global Music Control ${sourceId}: Control ${granted ? 'granted' : 'denied'}`);
    return granted;
  }, [sourceId, requestAudioControl]);

  // Release control function
  const releaseControl = useCallback(() => {
    releaseAudioControl(sourceId);
    hasControlRef.current = false;
  }, [sourceId, releaseAudioControl]);

  // Notify play start
  const notifyPlayStart = useCallback((track?: AudioSource['currentTrack']) => {
    // Request control when starting to play
    const granted = requestAudioControl(sourceId);
    hasControlRef.current = granted;

    if (granted) {
      updateAudioSource(sourceId, {
        isPlaying: true,
        currentTrack: track || null,
      });

      if (onPlayStart) {
        onPlayStart();
      }
    }

    return granted;
  }, [sourceId, requestAudioControl, updateAudioSource, onPlayStart]);

  // Notify pause
  const notifyPause = useCallback(() => {
    updateAudioSource(sourceId, {
      isPlaying: false,
    });

    if (onPause) {
      onPause();
    }
  }, [sourceId, updateAudioSource, onPause]);

  // Notify stop
  const notifyStop = useCallback(() => {
    updateAudioSource(sourceId, {
      isPlaying: false,
      currentTrack: null,
      currentTime: 0,
    });

    releaseAudioControl(sourceId);
    hasControlRef.current = false;

    if (onStop) {
      onStop();
    }
  }, [sourceId, updateAudioSource, releaseAudioControl, onStop]);

  // Update state function
  const updateState = useCallback((updates: Partial<AudioSource>) => {
    updateAudioSource(sourceId, updates);
  }, [sourceId, updateAudioSource]);

  // Pause others function
  const pauseOthers = useCallback(() => {
    pauseAllExcept(sourceId);
  }, [sourceId, pauseAllExcept]);

  // Pause all function
  const pauseAll = useCallback(() => {
    pauseAllSources();
  }, [pauseAllSources]);

  return {
    // State
    isActive,
    hasControl,
    canPlay,
    isRegistered,

    // Actions
    requestControl,
    releaseControl,
    notifyPlayStart,
    notifyPause,
    notifyStop,
    updateState,

    // Utilities
    pauseOthers,
    pauseAll,
  };
}

/**
 * Hook for components that need to listen to global music state changes
 */
export function useGlobalMusicState() {
  const globalAudioControl = useAudioStore((state) => state.globalAudioControl);
  const getActiveSource = useAudioStore((state) => state.getActiveSource);
  const getAllSources = useAudioStore((state) => state.getAllSources);

  const activeSource = getActiveSource();
  const allSources = getAllSources();

  return {
    currentActiveSource: globalAudioControl.currentActiveSource,
    activeSource,
    allSources,
    mutualExclusionEnabled: globalAudioControl.mutualExclusionEnabled,
    shouldAutoPlay: globalAudioControl.shouldAutoPlay,
  };
}
